import { createSlice, createAsyncThunk } from "@reduxjs/toolkit";
import axios from "axios";
import URL from "../../utils/api/API_URL";

const BASE_URL = `${URL}/api/tasks`;
const getAuthToken = () => localStorage.getItem("token");

// === Thunks for provided API endpoints ===

// 1. Create Task For Student
export const createTaskForStudent = createAsyncThunk(
  "tasks/createForStudent",
  async (payload, thunkAPI) => {
    try {
      const res = await axios.post(`${BASE_URL}/for-student`, payload, {
        headers: { Authorization: `Bearer ${getAuthToken()}` },
      });
      return res.data;
    } catch (err) {
      return thunkAPI.rejectWithValue(err.response?.data || err.message);
    }
  }
);

// 2. Create Task For Classroom
export const createTaskForClassroom = createAsyncThunk(
  "tasks/createForClassroom",
  async (payload, thunkAPI) => {
    try {
      const res = await axios.post(`${BASE_URL}/for-classroom/`, payload, {
        headers: { Authorization: `Bearer ${getAuthToken()}` },
      });
      return res.data;
    } catch (err) {
      return thunkAPI.rejectWithValue(err.response?.data || err.message);
    }
  }
);

// 3. Create Task For Multiple Students
export const createTaskForMultipleStudents = createAsyncThunk(
  "tasks/createForMultipleStudents",
  async (payload, thunkAPI) => {
    try {
      const res = await axios.post(`${BASE_URL}/for-multiple-students`, payload, {
        headers: { Authorization: `Bearer ${getAuthToken()}` },
      });
      return res.data;
    } catch (err) {
      return thunkAPI.rejectWithValue(err.response?.data || err.message);
    }
  }
);

// 4. Create Task For Multiple Classrooms
export const createTaskForMultipleClassrooms = createAsyncThunk(
  "tasks/createForMultipleClassrooms",
  async (payload, thunkAPI) => {
    try {
      const res = await axios.post(`${BASE_URL}/for-multiple-classrooms`, payload, {
        headers: { Authorization: `Bearer ${getAuthToken()}` },
      });
      return res.data;
    } catch (err) {
      return thunkAPI.rejectWithValue(err.response?.data || err.message);
    }
  }
);

// 5. Assign Task To Multiple Students
export const assignTaskToMultipleStudents = createAsyncThunk(
  "tasks/assignToMultipleStudents",
  async ({ task_id, studentIds }, thunkAPI) => {
    try {
      const res = await axios.post(
        `${BASE_URL}/${task_id}/students/bulk`,
        studentIds,
        { headers: { Authorization: `Bearer ${getAuthToken()}` } }
      );
      return res.data;
    } catch (err) {
      return thunkAPI.rejectWithValue(err.response?.data || err.message);
    }
  }
);

// 6. Get Task By Id (General - for teachers)
export const fetchTaskById = createAsyncThunk(
  "tasks/fetchById",
  async (task_id, thunkAPI) => {
    try {
      const res = await axios.get(`${BASE_URL}/${task_id}`, {
        headers: { Authorization: `Bearer ${getAuthToken()}` },
      });
      return res.data;
    } catch (err) {
      return thunkAPI.rejectWithValue(err.response?.data || err.message);
    }
  }
);

// 6b. Get Student Task By Id (Student-specific endpoint)
export const fetchStudentTaskById = createAsyncThunk(
  "tasks/fetchStudentTaskById",
  async (task_id, thunkAPI) => {
    try {
      const res = await axios.get(`${BASE_URL}/my/${task_id}`, {
        headers: { Authorization: `Bearer ${getAuthToken()}` },
      });
      return res.data;
    } catch (err) {
      return thunkAPI.rejectWithValue(err.response?.data || err.message);
    }
  }
);

// 7. Update Task
export const updateTask = createAsyncThunk(
  "tasks/update",
  async ({ task_id, data }, thunkAPI) => {
    try {
      const res = await axios.put(`${BASE_URL}/${task_id}`, data, {
        headers: { Authorization: `Bearer ${getAuthToken()}` },
      });
      return res.data;
    } catch (err) {
      return thunkAPI.rejectWithValue(err.response?.data || err.message);
    }
  }
);

// 8. Delete Task
export const deleteTask = createAsyncThunk(
  "tasks/delete",
  async (task_id, thunkAPI) => {
    try {
      await axios.delete(`${BASE_URL}/${task_id}`, {
        headers: { Authorization: `Bearer ${getAuthToken()}` },
      });
      return task_id;
    } catch (err) {
      return thunkAPI.rejectWithValue(err.response?.data || err.message);
    }
  }
);

// 9. Get All Tasks With Filters
export const fetchAllTasksWithFilters = createAsyncThunk(
  "tasks/fetchAllWithFilters",
  async (params, thunkAPI) => {
    try {
      const res = await axios.get(`${BASE_URL}/`, {
        headers: { Authorization: `Bearer ${getAuthToken()}` },
        params,
      });
      return res.data;
    } catch (err) {
      return thunkAPI.rejectWithValue(err.response?.data || err.message);
    }
  }
);

// 10. Get Tasks By Classroom
export const fetchTasksByClassroom = createAsyncThunk(
  "tasks/fetchByClassroom",
  async ({ classroom_id, skip = 0, limit = 100 }, thunkAPI) => {
    try {
      const res = await axios.get(`${BASE_URL}/classrooms/${classroom_id}`, {
        headers: { Authorization: `Bearer ${getAuthToken()}` },
        params: { skip, limit },
      });
      return res.data;
    } catch (err) {
      return thunkAPI.rejectWithValue(err.response?.data || err.message);
    }
  }
);

// 11. Get Tasks By Student (My Tasks)
export const fetchTasksByStudent = createAsyncThunk(
  "tasks/fetchByStudent",
  async ({ skip = 0, limit = 100 } = {}, thunkAPI) => {
    try {
      const res = await axios.get(`${BASE_URL}/my/tasks`, {
        headers: { Authorization: `Bearer ${getAuthToken()}` },
        params: { skip, limit },
      });
      return res.data;
    } catch (err) {
      return thunkAPI.rejectWithValue(err.response?.data || err.message);
    }
  }
);

// 12. Get Students By Task
export const fetchStudentsByTask = createAsyncThunk(
  "tasks/fetchStudentsByTask",
  async ({ task_id, skip = 0, limit = 100 }, thunkAPI) => {
    try {
      const res = await axios.get(`${BASE_URL}/${task_id}/students`, {
        headers: { Authorization: `Bearer ${getAuthToken()}` },
        params: { skip, limit },
      });
      return res.data;
    } catch (err) {
      return thunkAPI.rejectWithValue(err.response?.data || err.message);
    }
  }
);

// === Task Submission & Grading APIs ===

// 13. Submit Task (Student)
export const submitTask = createAsyncThunk(
  "tasks/submit",
  async ({ task_id, submission_data }, thunkAPI) => {
    try {
      const res = await axios.post(`${BASE_URL}/${task_id}/submit`, submission_data, {
        headers: { Authorization: `Bearer ${getAuthToken()}` },
      });
      return res.data;
    } catch (err) {
      return thunkAPI.rejectWithValue(err.response?.data || err.message);
    }
  }
);

// 14. Get Task Submissions (Teacher)
export const fetchTaskSubmissions = createAsyncThunk(
  "tasks/fetchSubmissions",
  async ({ task_id, skip = 0, limit = 100 }, thunkAPI) => {
    try {
      const res = await axios.get(`${BASE_URL}/${task_id}/submissions`, {
        headers: { Authorization: `Bearer ${getAuthToken()}` },
        params: { skip, limit },
      });
      return res.data;
    } catch (err) {
      return thunkAPI.rejectWithValue(err.response?.data || err.message);
    }
  }
);

// 15. Grade Task Submission (Teacher)
export const gradeTaskSubmission = createAsyncThunk(
  "tasks/gradeSubmission",
  async ({ task_id, student_id, grading_data }, thunkAPI) => {
    try {
      const res = await axios.post(`${BASE_URL}/${task_id}/grade/${student_id}`, grading_data, {
        headers: { Authorization: `Bearer ${getAuthToken()}` },
      });
      return res.data;
    } catch (err) {
      return thunkAPI.rejectWithValue(err.response?.data || err.message);
    }
  }
);

// 16. Get Student Task Submission
export const fetchStudentTaskSubmission = createAsyncThunk(
  "tasks/fetchStudentSubmission",
  async ({ task_id, student_id }, thunkAPI) => {
    try {
      const res = await axios.get(`${BASE_URL}/${task_id}/submission/${student_id}`, {
        headers: { Authorization: `Bearer ${getAuthToken()}` },
      });
      return res.data;
    } catch (err) {
      return thunkAPI.rejectWithValue(err.response?.data || err.message);
    }
  }
);

// === File Attachment APIs ===

// 17. Upload Task Attachment
export const uploadTaskAttachment = createAsyncThunk(
  "tasks/uploadAttachment",
  async ({ task_id, file, attachment_type = "submission" }, thunkAPI) => {
    try {
      const formData = new FormData();
      formData.append("file", file);
      formData.append("attachment_type", attachment_type);

      const res = await axios.post(`${BASE_URL}/${task_id}/attachments`, formData, {
        headers: {
          Authorization: `Bearer ${getAuthToken()}`,
          "Content-Type": "multipart/form-data"
        },
      });
      return res.data;
    } catch (err) {
      return thunkAPI.rejectWithValue(err.response?.data || err.message);
    }
  }
);

// 18. Get Task Attachments
export const fetchTaskAttachments = createAsyncThunk(
  "tasks/fetchAttachments",
  async ({ task_id, attachment_type }, thunkAPI) => {
    try {
      const params = attachment_type ? { attachment_type } : {};
      const res = await axios.get(`${BASE_URL}/${task_id}/attachments`, {
        headers: { Authorization: `Bearer ${getAuthToken()}` },
        params,
      });
      return res.data;
    } catch (err) {
      return thunkAPI.rejectWithValue(err.response?.data || err.message);
    }
  }
);

// 19. Delete Task Attachment
export const deleteTaskAttachment = createAsyncThunk(
  "tasks/deleteAttachment",
  async ({ task_id, attachment_id }, thunkAPI) => {
    try {
      await axios.delete(`${BASE_URL}/${task_id}/attachments/${attachment_id}`, {
        headers: { Authorization: `Bearer ${getAuthToken()}` },
      });
      return { task_id, attachment_id };
    } catch (err) {
      return thunkAPI.rejectWithValue(err.response?.data || err.message);
    }
  }
);

// 20. Update Task Status
export const updateTaskStatus = createAsyncThunk(
  "tasks/updateStatus",
  async ({ task_id, status, student_id }, thunkAPI) => {
    try {
      const payload = { status };
      if (student_id) payload.student_id = student_id;

      const res = await axios.patch(`${BASE_URL}/${task_id}/status`, payload, {
        headers: { Authorization: `Bearer ${getAuthToken()}` },
      });
      return res.data;
    } catch (err) {
      return thunkAPI.rejectWithValue(err.response?.data || err.message);
    }
  }
);

// === Initial State ===
const initialState = {
  tasks: [],
  task: null,
  loading: false,
  error: null,

  // Submission management
  submissions: [],
  currentSubmission: null,
  submissionLoading: false,
  submissionError: null,

  // Grading management
  grading: {
    loading: false,
    error: null,
    success: false,
  },

  // Attachment management
  attachments: [],
  attachmentLoading: false,
  attachmentError: null,

  // Status management
  statusUpdate: {
    loading: false,
    error: null,
    success: false,
  },
};

// === Slice ===
const taskSlice = createSlice({
  name: "tasks",
  initialState,
  reducers: {
    clearTaskState: (state) => {
      state.loading = false;
      state.error = null;
      state.task = null;
    },
    clearSubmissionState: (state) => {
      state.submissionLoading = false;
      state.submissionError = null;
      state.currentSubmission = null;
    },
    clearGradingState: (state) => {
      state.grading.loading = false;
      state.grading.error = null;
      state.grading.success = false;
    },
    clearAttachmentState: (state) => {
      state.attachmentLoading = false;
      state.attachmentError = null;
    },
    clearStatusUpdateState: (state) => {
      state.statusUpdate.loading = false;
      state.statusUpdate.error = null;
      state.statusUpdate.success = false;
    },
    setCurrentSubmission: (state, action) => {
      state.currentSubmission = action.payload;
    },
  },
  extraReducers: (builder) => {
    // Add loading/error/success handling for each thunk
    builder
      // Create Task For Student
      .addCase(createTaskForStudent.pending, (state) => { state.loading = true; state.error = null; })
      .addCase(createTaskForStudent.fulfilled, (state, action) => { state.loading = false; state.tasks.push(action.payload); })
      .addCase(createTaskForStudent.rejected, (state, action) => { state.loading = false; state.error = action.payload; })
      // Create Task For Classroom
      .addCase(createTaskForClassroom.pending, (state) => { state.loading = true; state.error = null; })
      .addCase(createTaskForClassroom.fulfilled, (state, action) => { state.loading = false; state.tasks.push(action.payload); })
      .addCase(createTaskForClassroom.rejected, (state, action) => { state.loading = false; state.error = action.payload; })
      // Create Task For Multiple Students
      .addCase(createTaskForMultipleStudents.pending, (state) => { state.loading = true; state.error = null; })
      .addCase(createTaskForMultipleStudents.fulfilled, (state, action) => { state.loading = false; state.tasks.push(action.payload); })
      .addCase(createTaskForMultipleStudents.rejected, (state, action) => { state.loading = false; state.error = action.payload; })
      // Create Task For Multiple Classrooms
      .addCase(createTaskForMultipleClassrooms.pending, (state) => { state.loading = true; state.error = null; })
      .addCase(createTaskForMultipleClassrooms.fulfilled, (state, action) => { state.loading = false; state.tasks.push(action.payload); })
      .addCase(createTaskForMultipleClassrooms.rejected, (state, action) => { state.loading = false; state.error = action.payload; })
      // Assign Task To Multiple Students
      .addCase(assignTaskToMultipleStudents.pending, (state) => { state.loading = true; state.error = null; })
      .addCase(assignTaskToMultipleStudents.fulfilled, (state) => { state.loading = false; })
      .addCase(assignTaskToMultipleStudents.rejected, (state, action) => { state.loading = false; state.error = action.payload; })
      // Get Task By Id
      .addCase(fetchTaskById.pending, (state) => { state.loading = true; state.error = null; state.task = null; })
      .addCase(fetchTaskById.fulfilled, (state, action) => { state.loading = false; state.task = action.payload; })
      .addCase(fetchTaskById.rejected, (state, action) => { state.loading = false; state.error = action.payload; })
      // Get Student Task By Id
      .addCase(fetchStudentTaskById.pending, (state) => { state.loading = true; state.error = null; state.task = null; })
      .addCase(fetchStudentTaskById.fulfilled, (state, action) => { state.loading = false; state.task = action.payload; })
      .addCase(fetchStudentTaskById.rejected, (state, action) => { state.loading = false; state.error = action.payload; })
      // Update Task
      .addCase(updateTask.pending, (state) => { state.loading = true; state.error = null; })
      .addCase(updateTask.fulfilled, (state, action) => { state.loading = false; state.task = action.payload; })
      .addCase(updateTask.rejected, (state, action) => { state.loading = false; state.error = action.payload; })
      // Delete Task
      .addCase(deleteTask.pending, (state) => { state.loading = true; state.error = null; })
      .addCase(deleteTask.fulfilled, (state, action) => { state.loading = false; state.tasks = state.tasks.filter(t => t.id !== action.payload); })
      .addCase(deleteTask.rejected, (state, action) => { state.loading = false; state.error = action.payload; })
      // Get All Tasks With Filters
      .addCase(fetchAllTasksWithFilters.pending, (state) => { state.loading = true; state.error = null; })
      .addCase(fetchAllTasksWithFilters.fulfilled, (state, action) => { state.loading = false; state.tasks = action.payload?.tasks || []; })
      .addCase(fetchAllTasksWithFilters.rejected, (state, action) => { state.loading = false; state.error = action.payload; })
      // Get Tasks By Classroom
      .addCase(fetchTasksByClassroom.pending, (state) => { state.loading = true; state.error = null; })
      .addCase(fetchTasksByClassroom.fulfilled, (state, action) => { state.loading = false; state.tasks = action.payload?.tasks || []; })
      .addCase(fetchTasksByClassroom.rejected, (state, action) => { state.loading = false; state.error = action.payload; })
      // Get Tasks By Student (My Tasks)
      .addCase(fetchTasksByStudent.pending, (state) => { state.loading = true; state.error = null; })
      .addCase(fetchTasksByStudent.fulfilled, (state, action) => { state.loading = false; state.tasks = action.payload?.tasks || []; })
      .addCase(fetchTasksByStudent.rejected, (state, action) => { state.loading = false; state.error = action.payload; })
      // Get Students By Task
      .addCase(fetchStudentsByTask.pending, (state) => { state.loading = true; state.error = null; })
      .addCase(fetchStudentsByTask.fulfilled, (state, action) => { state.loading = false; state.task = { ...state.task, students: action.payload } })
      .addCase(fetchStudentsByTask.rejected, (state, action) => { state.loading = false; state.error = action.payload; })

      // === Submission & Grading Reducers ===

      // Submit Task
      .addCase(submitTask.pending, (state) => { state.submissionLoading = true; state.submissionError = null; })
      .addCase(submitTask.fulfilled, (state, action) => {
        state.submissionLoading = false;
        state.currentSubmission = action.payload;
        // Update task status if included in response
        if (state.task && action.payload.task_id === state.task.id) {
          state.task.submission_status = action.payload.status;
        }
      })
      .addCase(submitTask.rejected, (state, action) => { state.submissionLoading = false; state.submissionError = action.payload; })

      // Fetch Task Submissions
      .addCase(fetchTaskSubmissions.pending, (state) => { state.submissionLoading = true; state.submissionError = null; })
      .addCase(fetchTaskSubmissions.fulfilled, (state, action) => {
        state.submissionLoading = false;
        state.submissions = action.payload?.submissions || [];
      })
      .addCase(fetchTaskSubmissions.rejected, (state, action) => { state.submissionLoading = false; state.submissionError = action.payload; })

      // Grade Task Submission
      .addCase(gradeTaskSubmission.pending, (state) => { state.grading.loading = true; state.grading.error = null; state.grading.success = false; })
      .addCase(gradeTaskSubmission.fulfilled, (state, action) => {
        state.grading.loading = false;
        state.grading.success = true;
        // Update submission in submissions array
        const submissionIndex = state.submissions.findIndex(s => s.student_id === action.payload.student_id);
        if (submissionIndex !== -1) {
          state.submissions[submissionIndex] = { ...state.submissions[submissionIndex], ...action.payload };
        }
      })
      .addCase(gradeTaskSubmission.rejected, (state, action) => { state.grading.loading = false; state.grading.error = action.payload; })

      // Fetch Student Task Submission
      .addCase(fetchStudentTaskSubmission.pending, (state) => { state.submissionLoading = true; state.submissionError = null; })
      .addCase(fetchStudentTaskSubmission.fulfilled, (state, action) => {
        state.submissionLoading = false;
        state.currentSubmission = action.payload;
      })
      .addCase(fetchStudentTaskSubmission.rejected, (state, action) => { state.submissionLoading = false; state.submissionError = action.payload; })

      // === Attachment Reducers ===

      // Upload Task Attachment
      .addCase(uploadTaskAttachment.pending, (state) => { state.attachmentLoading = true; state.attachmentError = null; })
      .addCase(uploadTaskAttachment.fulfilled, (state, action) => {
        state.attachmentLoading = false;
        state.attachments.push(action.payload);
      })
      .addCase(uploadTaskAttachment.rejected, (state, action) => { state.attachmentLoading = false; state.attachmentError = action.payload; })

      // Fetch Task Attachments
      .addCase(fetchTaskAttachments.pending, (state) => { state.attachmentLoading = true; state.attachmentError = null; })
      .addCase(fetchTaskAttachments.fulfilled, (state, action) => {
        state.attachmentLoading = false;
        state.attachments = action.payload?.attachments || [];
      })
      .addCase(fetchTaskAttachments.rejected, (state, action) => { state.attachmentLoading = false; state.attachmentError = action.payload; })

      // Delete Task Attachment
      .addCase(deleteTaskAttachment.pending, (state) => { state.attachmentLoading = true; state.attachmentError = null; })
      .addCase(deleteTaskAttachment.fulfilled, (state, action) => {
        state.attachmentLoading = false;
        state.attachments = state.attachments.filter(a => a.id !== action.payload.attachment_id);
      })
      .addCase(deleteTaskAttachment.rejected, (state, action) => { state.attachmentLoading = false; state.attachmentError = action.payload; })

      // === Status Update Reducers ===

      // Update Task Status
      .addCase(updateTaskStatus.pending, (state) => { state.statusUpdate.loading = true; state.statusUpdate.error = null; state.statusUpdate.success = false; })
      .addCase(updateTaskStatus.fulfilled, (state, action) => {
        state.statusUpdate.loading = false;
        state.statusUpdate.success = true;
        // Update task status
        if (state.task && action.payload.task_id === state.task.id) {
          state.task.status = action.payload.status;
        }
        // Update task in tasks array
        const taskIndex = state.tasks.findIndex(t => t.id === action.payload.task_id);
        if (taskIndex !== -1) {
          state.tasks[taskIndex].status = action.payload.status;
        }
      })
      .addCase(updateTaskStatus.rejected, (state, action) => { state.statusUpdate.loading = false; state.statusUpdate.error = action.payload; });
  },
});

// === Exports ===
export const {
  clearTaskState,
  clearSubmissionState,
  clearGradingState,
  clearAttachmentState,
  clearStatusUpdateState,
  setCurrentSubmission
} = taskSlice.actions;

// === Selectors ===
export const selectTasks = (state) => state.tasks.tasks;
export const selectCurrentTask = (state) => state.tasks.task;
export const selectTasksLoading = (state) => state.tasks.loading;
export const selectTasksError = (state) => state.tasks.error;

export const selectSubmissions = (state) => state.tasks.submissions;
export const selectCurrentSubmission = (state) => state.tasks.currentSubmission;
export const selectSubmissionLoading = (state) => state.tasks.submissionLoading;
export const selectSubmissionError = (state) => state.tasks.submissionError;

export const selectGradingState = (state) => state.tasks.grading;
export const selectAttachments = (state) => state.tasks.attachments;
export const selectAttachmentLoading = (state) => state.tasks.attachmentLoading;
export const selectAttachmentError = (state) => state.tasks.attachmentError;

export const selectStatusUpdateState = (state) => state.tasks.statusUpdate;

export default taskSlice.reducer;
