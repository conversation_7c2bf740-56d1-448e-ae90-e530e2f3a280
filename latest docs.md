for listing all tasks 

curl -X 'GET' \
  'https://edufair.duckdns.org/api/tasks/my/tasks?skip=0&limit=100' \
  -H 'accept: application/json'

response:
{
  "tasks": [
    {
      "id": "3fa85f64-5717-4562-b3fc-2c963f66afa6",
      "name": "string",
      "description": "string",
      "deadline": "2025-08-05T14:35:42.940Z",
      "status": "pending",
      "accept_after_deadline": false,
      "subject": {
        "name": "string",
        "id": "3fa85f64-5717-4562-b3fc-2c963f66afa6"
      }
    }
  ],
  "total": 0
}

for getting details of a single task using task id 


curl -X 'GET' \
  'https://edufair.duckdns.org/api/tasks/my/3fa85f64-5717-4562-b3fc-2c963f66afa6' \
  -H 'accept: application/json'

  {
  "id": "3fa85f64-5717-4562-b3fc-2c963f66afa6",
  "name": "string",
  "description": "string",
  "status": "pending",
  "deadline": "2025-08-05T14:37:22.839Z",
  "accept_after_deadline": false,
  "created_at": "2025-08-05T14:37:22.839Z",
  "updated_at": "2025-08-05T14:37:22.839Z",
  "subject": {
    "name": "string",
    "id": "3fa85f64-5717-4562-b3fc-2c963f66afa6"
  },
  "chapters": [],
  "topics": [],
  "subtopics": [],
  "teacher_attachments": [],
  "my_attachments": []
}